import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import '../providers/app_state_provider.dart';
import '../providers/transcript_provider.dart';
import '../models/consultation.dart';
import '../services/pdf_service.dart';
import '../services/storage_service.dart';
import '../widgets/app_scaffold.dart';

class ReviewTranscriptScreen extends StatefulWidget {
  const ReviewTranscriptScreen({super.key});

  @override
  State<ReviewTranscriptScreen> createState() => _ReviewTranscriptScreenState();
}

class _ReviewTranscriptScreenState extends State<ReviewTranscriptScreen> {
  final _notesController = TextEditingController();
  bool _isGeneratingPdf = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _initializeNotes();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  void _initializeNotes() {
    final transcriptProvider = context.read<TranscriptProvider>();
    if (transcriptProvider.currentTranscript?.doctorNotes != null) {
      _notesController.text = transcriptProvider.currentTranscript!.doctorNotes!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AppStateProvider, TranscriptProvider>(
      builder: (context, appState, transcriptProvider, child) {
        final consultation = appState.currentConsultation;
        final transcript = transcriptProvider.currentTranscript;
        
        if (consultation == null || transcript == null) {
          return const Scaffold(
            body: Center(
              child: Text('No transcript data available'),
            ),
          );
        }

        return AppScaffold(
          title: 'Review Transcript',
          onBackPressed: () => _handleBackPressed(appState),
          body: _buildBody(context, appState, transcriptProvider, consultation, transcript),
        );
      },
    );
  }

  Widget _buildBody(
    BuildContext context,
    AppStateProvider appState,
    TranscriptProvider transcriptProvider,
    consultation,
    transcript,
  ) {
    return Column(
      children: [
        // Patient header
        _buildPatientHeader(consultation),
        
        // Transcript content
        Expanded(
          child: _buildTranscriptContent(transcriptProvider, transcript),
        ),

        // Doctor's notes section
        _buildNotesSection(transcriptProvider),

        // Action buttons
        _buildActionButtons(context, appState, transcriptProvider, consultation),
      ],
    );
  }

  Widget _buildPatientHeader(consultation) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).primaryColor.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: Theme.of(context).primaryColor,
            child: Text(
              consultation.patient.name.substring(0, 1).toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  consultation.patient.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${consultation.patient.age} years, ${consultation.patient.gender}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                if (consultation.patient.reasonForVisit != null)
                  Text(
                    consultation.patient.reasonForVisit!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                _formatDate(consultation.createdAt),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              if (consultation.duration != null)
                Text(
                  consultation.formattedDuration,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTranscriptContent(TranscriptProvider transcriptProvider, transcript) {
    final segments = transcript.doctorPatientSegments;

    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.transcript,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Consultation Transcript',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _isEditing = !_isEditing;
                    });
                  },
                  icon: Icon(_isEditing ? Icons.done : Icons.edit),
                  label: Text(_isEditing ? 'Done' : 'Edit'),
                ),
              ],
            ),
          ),

          // Transcript segments
          Expanded(
            child: segments.isEmpty
                ? _buildEmptyTranscript()
                : _buildTranscriptList(segments, transcriptProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyTranscript() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.transcript,
            size: 48,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No transcript available',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTranscriptList(segments, TranscriptProvider transcriptProvider) {
    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: segments.length,
      itemBuilder: (context, index) {
        final segment = segments[index];
        return _buildTranscriptSegment(segment, transcriptProvider);
      },
    );
  }

  Widget _buildTranscriptSegment(segment, TranscriptProvider transcriptProvider) {
    final isDoctor = segment.speaker.name == 'doctor';
    final speakerColor = isDoctor ? Colors.blue : Colors.green;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timestamp
          SizedBox(
            width: 80,
            child: Text(
              segment.formattedTimestamp,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontFamily: 'monospace',
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Speaker indicator
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: speakerColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: speakerColor.withOpacity(0.3),
              ),
            ),
            child: Center(
              child: Text(
                isDoctor ? 'D' : 'P',
                style: TextStyle(
                  color: speakerColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Transcript text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  segment.speakerLabel,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: speakerColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                _isEditing
                    ? _buildEditableText(segment, transcriptProvider)
                    : Text(
                        segment.text,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditableText(segment, TranscriptProvider transcriptProvider) {
    return TextFormField(
      initialValue: segment.text,
      maxLines: null,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        isDense: true,
      ),
      onChanged: (value) {
        transcriptProvider.editTranscriptSegment(segment.id, value);
      },
    );
  }

  Widget _buildNotesSection(TranscriptProvider transcriptProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.note_add,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Doctor\'s Notes & Impression',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: _notesController,
                maxLines: 4,
                decoration: const InputDecoration(
                  hintText: 'Add your clinical notes, impression, and treatment plan...',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  transcriptProvider.updateDoctorNotes(value);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    AppStateProvider appState,
    TranscriptProvider transcriptProvider,
    consultation,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => _startNewConsultation(appState),
              child: const Text('New Consultation'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _isGeneratingPdf
                  ? null
                  : () => _generatePdf(context, appState, transcriptProvider, consultation),
              child: _isGeneratingPdf
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Generate PDF'),
            ),
          ),
        ],
      ),
    );
  }

  void _generatePdf(
    BuildContext context,
    AppStateProvider appState,
    TranscriptProvider transcriptProvider,
    consultation,
  ) async {
    setState(() => _isGeneratingPdf = true);

    try {
      // Update consultation with final transcript
      final updatedConsultation = consultation.copyWith(
        transcript: transcriptProvider.currentTranscript,
        status: ConsultationStatus.completed,
        completedAt: DateTime.now(),
      );

      // Generate PDF
      final pdfPath = await PdfService.instance.generateConsultationReport(
        updatedConsultation,
        doctorName: appState.doctorName,
        clinicName: 'DocTranscribe Clinic', // This could be configurable
      );

      // Update consultation with PDF path
      final finalConsultation = updatedConsultation.copyWith(
        pdfFilePath: pdfPath,
      );

      // Save to storage
      await StorageService.instance.saveConsultation(finalConsultation);

      // Update app state
      appState.updateCurrentConsultation(finalConsultation);

      // Show success and offer to share
      _showPdfGeneratedDialog(context, pdfPath);
    } catch (e) {
      _showErrorSnackBar('Failed to generate PDF: $e');
    } finally {
      setState(() => _isGeneratingPdf = false);
    }
  }

  void _showPdfGeneratedDialog(BuildContext context, String pdfPath) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('PDF Generated'),
        content: const Text('Your consultation report has been generated successfully.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _sharePdf(pdfPath);
            },
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }

  void _sharePdf(String pdfPath) async {
    try {
      await Share.shareXFiles([XFile(pdfPath)]);
    } catch (e) {
      _showErrorSnackBar('Failed to share PDF: $e');
    }
  }

  void _startNewConsultation(AppStateProvider appState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Start New Consultation'),
        content: const Text('Are you sure you want to start a new consultation? Make sure you have saved the current one.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              appState.navigateToHome();
            },
            child: const Text('Start New'),
          ),
        ],
      ),
    );
  }

  void _handleBackPressed(AppStateProvider appState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Go Back'),
        content: const Text('Are you sure you want to go back? Make sure you have generated the PDF report.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Stay'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              appState.navigateToHome();
            },
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
