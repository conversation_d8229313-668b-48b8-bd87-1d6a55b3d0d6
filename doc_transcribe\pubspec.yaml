name: doc_transcribe
description: A Flutter app for doctors to record, transcribe, and generate PDF reports of patient consultations.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  
  # Audio Recording
  flutter_sound: ^9.2.13
  permission_handler: ^11.1.0
  
  # gRPC for NVIDIA Riva
  grpc: ^3.2.4
  protobuf: ^3.1.0
  
  # PDF Generation
  pdf: ^3.10.7
  printing: ^5.11.1
  
  # Local Storage
  sqflite: ^2.3.0
  path_provider: ^2.1.1
  flutter_secure_storage: ^9.0.0
  
  # File Sharing
  share_plus: ^7.2.1
  
  # UI Components
  cupertino_icons: ^1.0.6
  file_picker: ^6.1.1
  
  # Utilities
  intl: ^0.18.1
  uuid: ^4.2.1
  crypto: ^3.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
