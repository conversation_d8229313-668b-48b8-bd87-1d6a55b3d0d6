import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_state_provider.dart';
import '../providers/recording_provider.dart';
import '../providers/transcript_provider.dart';
import '../widgets/app_scaffold.dart';
import '../widgets/recording_controls.dart';
import '../widgets/live_transcript_widget.dart';
import '../widgets/audio_visualizer.dart';

class RecordingScreen extends StatefulWidget {
  const RecordingScreen({super.key});

  @override
  State<RecordingScreen> createState() => _RecordingScreenState();
}

class _RecordingScreenState extends State<RecordingScreen> {
  bool _showLiveTranscript = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeRecording();
    });
  }

  void _initializeRecording() async {
    final recordingProvider = context.read<RecordingProvider>();
    try {
      await recordingProvider.initialize();
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Failed to initialize recording: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<AppStateProvider, RecordingProvider, TranscriptProvider>(
      builder: (context, appState, recordingProvider, transcriptProvider, child) {
        final consultation = appState.currentConsultation;
        
        if (consultation == null) {
          return const Scaffold(
            body: Center(
              child: Text('No consultation data available'),
            ),
          );
        }

        return AppScaffold(
          title: 'Recording Consultation',
          onBackPressed: () => _handleBackPressed(context, appState, recordingProvider),
          body: _buildBody(context, appState, recordingProvider, transcriptProvider, consultation),
        );
      },
    );
  }

  Widget _buildBody(
    BuildContext context,
    AppStateProvider appState,
    RecordingProvider recordingProvider,
    TranscriptProvider transcriptProvider,
    consultation,
  ) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Patient info header
          _buildPatientHeader(consultation),
          
          const SizedBox(height: 24),

          // Recording status and controls
          Expanded(
            flex: 2,
            child: _buildRecordingSection(
              context,
              appState,
              recordingProvider,
              transcriptProvider,
            ),
          ),

          const SizedBox(height: 24),

          // Live transcript section
          if (_showLiveTranscript) ...[
            Expanded(
              flex: 3,
              child: _buildLiveTranscriptSection(transcriptProvider),
            ),
            const SizedBox(height: 16),
          ],

          // Control buttons
          _buildControlButtons(
            context,
            appState,
            recordingProvider,
            transcriptProvider,
          ),
        ],
      ),
    );
  }

  Widget _buildPatientHeader(consultation) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: Theme.of(context).primaryColor,
              child: Text(
                consultation.patient.name.substring(0, 1).toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    consultation.patient.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${consultation.patient.age} years, ${consultation.patient.gender}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  if (consultation.patient.reasonForVisit != null)
                    Text(
                      consultation.patient.reasonForVisit!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingSection(
    BuildContext context,
    AppStateProvider appState,
    RecordingProvider recordingProvider,
    TranscriptProvider transcriptProvider,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Recording status
            _buildRecordingStatus(recordingProvider),
            
            const SizedBox(height: 24),

            // Audio visualizer
            AudioVisualizer(
              isRecording: recordingProvider.isRecording,
              audioLevel: recordingProvider.audioLevel,
            ),

            const SizedBox(height: 24),

            // Recording duration
            Text(
              recordingProvider.formattedDuration,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                fontFamily: 'monospace',
              ),
            ),

            const SizedBox(height: 24),

            // Recording controls
            RecordingControls(
              recordingState: recordingProvider.state,
              onStartRecording: () => _startRecording(
                appState,
                recordingProvider,
                transcriptProvider,
              ),
              onStopRecording: () => _stopRecording(
                context,
                appState,
                recordingProvider,
                transcriptProvider,
              ),
              onPauseRecording: () => recordingProvider.pauseRecording(),
              onResumeRecording: () => recordingProvider.resumeRecording(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingStatus(RecordingProvider recordingProvider) {
    String statusText;
    Color statusColor;
    IconData statusIcon;

    switch (recordingProvider.state) {
      case RecordingState.idle:
        statusText = 'Ready to Record';
        statusColor = Colors.grey;
        statusIcon = Icons.mic_off;
        break;
      case RecordingState.recording:
        statusText = 'Recording...';
        statusColor = Colors.red;
        statusIcon = Icons.mic;
        break;
      case RecordingState.paused:
        statusText = 'Paused';
        statusColor = Colors.orange;
        statusIcon = Icons.pause;
        break;
      case RecordingState.stopped:
        statusText = 'Recording Stopped';
        statusColor = Colors.blue;
        statusIcon = Icons.stop;
        break;
      case RecordingState.processing:
        statusText = 'Processing...';
        statusColor = Colors.purple;
        statusIcon = Icons.hourglass_empty;
        break;
      case RecordingState.error:
        statusText = 'Error: ${recordingProvider.errorMessage ?? "Unknown error"}';
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(statusIcon, color: statusColor, size: 24),
        const SizedBox(width: 8),
        Text(
          statusText,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: statusColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildLiveTranscriptSection(TranscriptProvider transcriptProvider) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.live_tv,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Live Transcript',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _showLiveTranscript = false;
                    });
                  },
                  icon: const Icon(Icons.close, size: 20),
                ),
              ],
            ),
          ),
          Expanded(
            child: LiveTranscriptWidget(
              segments: transcriptProvider.liveSegments,
              isStreaming: transcriptProvider.isStreaming,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons(
    BuildContext context,
    AppStateProvider appState,
    RecordingProvider recordingProvider,
    TranscriptProvider transcriptProvider,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Live transcript toggle
        if (!_showLiveTranscript && recordingProvider.isRecording)
          OutlinedButton.icon(
            onPressed: () {
              setState(() {
                _showLiveTranscript = true;
              });
            },
            icon: const Icon(Icons.live_tv),
            label: const Text('Show Live Transcript'),
          ),

        if (_showLiveTranscript || !recordingProvider.isRecording)
          const SizedBox(height: 16),

        // Action buttons
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: recordingProvider.canRecord
                    ? () => _cancelRecording(context, appState, recordingProvider)
                    : null,
                child: const Text('Cancel'),
              ),
            ),
            if (recordingProvider.isStopped) ...[
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: () => _proceedToReview(
                    context,
                    appState,
                    transcriptProvider,
                  ),
                  child: const Text('Review Transcript'),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  // Event handlers
  void _startRecording(
    AppStateProvider appState,
    RecordingProvider recordingProvider,
    TranscriptProvider transcriptProvider,
  ) async {
    try {
      await recordingProvider.startRecording(
        apiKey: appState.apiKey!,
        language: appState.selectedLanguage,
      );

      // Start live transcription
      await transcriptProvider.startLiveTranscription(
        transcriptId: appState.currentConsultation!.id,
        language: appState.selectedLanguage,
      );
    } catch (e) {
      _showErrorDialog('Failed to start recording: $e');
    }
  }

  void _stopRecording(
    BuildContext context,
    AppStateProvider appState,
    RecordingProvider recordingProvider,
    TranscriptProvider transcriptProvider,
  ) async {
    try {
      final audioFilePath = await recordingProvider.stopRecording();
      await transcriptProvider.stopLiveTranscription();

      // Update consultation with audio file path
      if (audioFilePath != null) {
        final updatedConsultation = appState.currentConsultation!.copyWith(
          audioFilePath: audioFilePath,
          status: ConsultationStatus.processing,
        );
        appState.updateCurrentConsultation(updatedConsultation);
      }
    } catch (e) {
      _showErrorDialog('Failed to stop recording: $e');
    }
  }

  void _cancelRecording(
    BuildContext context,
    AppStateProvider appState,
    RecordingProvider recordingProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Recording'),
        content: const Text('Are you sure you want to cancel this recording? All data will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue Recording'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await recordingProvider.cancelRecording();
              appState.navigateToHome();
            },
            child: const Text('Cancel Recording'),
          ),
        ],
      ),
    );
  }

  void _proceedToReview(
    BuildContext context,
    AppStateProvider appState,
    TranscriptProvider transcriptProvider,
  ) {
    if (transcriptProvider.hasTranscript) {
      appState.navigateToReview();
    } else {
      _showErrorDialog('No transcript available to review');
    }
  }

  void _handleBackPressed(
    BuildContext context,
    AppStateProvider appState,
    RecordingProvider recordingProvider,
  ) {
    if (recordingProvider.isRecording) {
      _cancelRecording(context, appState, recordingProvider);
    } else {
      appState.navigateToHome();
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
